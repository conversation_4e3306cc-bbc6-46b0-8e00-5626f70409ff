Refactor: Extract Visual Annotation Logic from ChessGameViewModel

This commit refactors the `ChessGameViewModel` to improve separation of concerns by extracting all logic for handling visual annotations (square highlights and arrows) into a new, dedicated `VisualAnnotationManager` class.

Key changes include:

1.  **New `VisualAnnotationManager` Class**: A new file `MacChessBase/ViewModels/VisualAnnotationManager.swift` was created. This class is an `ObservableObject` that now manages the state and logic for visual annotations, such as the current annotation color and the methods for toggling highlights and arrows.

2.  **Simplified `ChessGameViewModel`**: The `ChessGameViewModel` has been simplified by removing the annotation-related properties and methods. It now owns an instance of `VisualAnnotationManager` and delegates all annotation tasks to it.

3.  **Updated `ChessBoardView`**: The view was updated to call the new `visualAnnotationManager` for handling user interactions related to creating and modifying visual annotations.

4.  **Initialization Fixes**: Corrected Swift's two-phase initialization requirements to resolve a "self used before being initialized" compiler error, ensuring a safe and correct setup between the view model and its new child manager.

This change improves code modularity and maintainability without altering user-facing functionality.