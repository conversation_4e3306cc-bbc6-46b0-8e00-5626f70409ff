// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		8E2811552DF3EDBF008D9593 /* ChessKit in Frameworks */ = {isa = PBXBuildFile; productRef = 8E2811542DF3EDBF008D9593 /* ChessKit */; };
		8E2ACA762E39040E00C73355 /* SwiftSoup in Frameworks */ = {isa = PBXBuildFile; productRef = 8E2ACA752E39040E00C73355 /* SwiftSoup */; };
		8E7A115E2E1DF17C00880849 /* ChessKit in Frameworks */ = {isa = PBXBuildFile; productRef = 8E7A115D2E1DF17C00880849 /* ChessKit */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		8E6148222DEAB7930032F0FE /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 8E6148062DEAB78F0032F0FE /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 8E61480D2DEAB78F0032F0FE;
			remoteInfo = MacChessBase;
		};
		8E61482C2DEAB7930032F0FE /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 8E6148062DEAB78F0032F0FE /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 8E61480D2DEAB78F0032F0FE;
			remoteInfo = MacChessBase;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		8E4697172DEC032A00084789 /* todo.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; path = todo.md; sourceTree = "<group>"; };
		8E4B22232E3F0C5C00270D45 /* MacChessBase.xctestplan */ = {isa = PBXFileReference; lastKnownFileType = text; path = MacChessBase.xctestplan; sourceTree = "<group>"; };
		8E61480E2DEAB78F0032F0FE /* MacChessBase.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = MacChessBase.app; sourceTree = BUILT_PRODUCTS_DIR; };
		8E6148212DEAB7930032F0FE /* MacChessBaseTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = MacChessBaseTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		8E61482B2DEAB7930032F0FE /* MacChessBaseUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = MacChessBaseUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		8E6E442C2DF86E5400659BDB /* PGN.txt */ = {isa = PBXFileReference; lastKnownFileType = text; path = PGN.txt; sourceTree = "<group>"; };
		8EB65A752DEAB8FE0059C16B /* README.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; path = README.md; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		8E6148102DEAB78F0032F0FE /* MacChessBase */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = MacChessBase;
			sourceTree = "<group>";
		};
		8E6148242DEAB7930032F0FE /* MacChessBaseTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = MacChessBaseTests;
			sourceTree = "<group>";
		};
		8E61482E2DEAB7930032F0FE /* MacChessBaseUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = MacChessBaseUITests;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		8E61480B2DEAB78F0032F0FE /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				8E2811552DF3EDBF008D9593 /* ChessKit in Frameworks */,
				8E2ACA762E39040E00C73355 /* SwiftSoup in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		8E61481E2DEAB7930032F0FE /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				8E7A115E2E1DF17C00880849 /* ChessKit in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		8E6148282DEAB7930032F0FE /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		8E4697142DEBF2D500084789 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		8E6148052DEAB78F0032F0FE = {
			isa = PBXGroup;
			children = (
				8E4B22232E3F0C5C00270D45 /* MacChessBase.xctestplan */,
				8E4697172DEC032A00084789 /* todo.md */,
				8EB65A752DEAB8FE0059C16B /* README.md */,
				8E6148102DEAB78F0032F0FE /* MacChessBase */,
				8E6148242DEAB7930032F0FE /* MacChessBaseTests */,
				8E61482E2DEAB7930032F0FE /* MacChessBaseUITests */,
				8E4697142DEBF2D500084789 /* Frameworks */,
				8E61480F2DEAB78F0032F0FE /* Products */,
				8E6E442C2DF86E5400659BDB /* PGN.txt */,
			);
			sourceTree = "<group>";
		};
		8E61480F2DEAB78F0032F0FE /* Products */ = {
			isa = PBXGroup;
			children = (
				8E61480E2DEAB78F0032F0FE /* MacChessBase.app */,
				8E6148212DEAB7930032F0FE /* MacChessBaseTests.xctest */,
				8E61482B2DEAB7930032F0FE /* MacChessBaseUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		8E61480D2DEAB78F0032F0FE /* MacChessBase */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 8E6148352DEAB7930032F0FE /* Build configuration list for PBXNativeTarget "MacChessBase" */;
			buildPhases = (
				8E61480A2DEAB78F0032F0FE /* Sources */,
				8E61480B2DEAB78F0032F0FE /* Frameworks */,
				8E61480C2DEAB78F0032F0FE /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				8E6148102DEAB78F0032F0FE /* MacChessBase */,
			);
			name = MacChessBase;
			packageProductDependencies = (
				8E2811542DF3EDBF008D9593 /* ChessKit */,
				8E2ACA752E39040E00C73355 /* SwiftSoup */,
			);
			productName = MacChessBase;
			productReference = 8E61480E2DEAB78F0032F0FE /* MacChessBase.app */;
			productType = "com.apple.product-type.application";
		};
		8E6148202DEAB7930032F0FE /* MacChessBaseTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 8E6148382DEAB7930032F0FE /* Build configuration list for PBXNativeTarget "MacChessBaseTests" */;
			buildPhases = (
				8E61481D2DEAB7930032F0FE /* Sources */,
				8E61481E2DEAB7930032F0FE /* Frameworks */,
				8E61481F2DEAB7930032F0FE /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				8E6148232DEAB7930032F0FE /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				8E6148242DEAB7930032F0FE /* MacChessBaseTests */,
			);
			name = MacChessBaseTests;
			packageProductDependencies = (
				8E7A115D2E1DF17C00880849 /* ChessKit */,
			);
			productName = MacChessBaseTests;
			productReference = 8E6148212DEAB7930032F0FE /* MacChessBaseTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		8E61482A2DEAB7930032F0FE /* MacChessBaseUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 8E61483B2DEAB7930032F0FE /* Build configuration list for PBXNativeTarget "MacChessBaseUITests" */;
			buildPhases = (
				8E6148272DEAB7930032F0FE /* Sources */,
				8E6148282DEAB7930032F0FE /* Frameworks */,
				8E6148292DEAB7930032F0FE /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				8E61482D2DEAB7930032F0FE /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				8E61482E2DEAB7930032F0FE /* MacChessBaseUITests */,
			);
			name = MacChessBaseUITests;
			packageProductDependencies = (
			);
			productName = MacChessBaseUITests;
			productReference = 8E61482B2DEAB7930032F0FE /* MacChessBaseUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		8E6148062DEAB78F0032F0FE /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1620;
				LastUpgradeCheck = 1620;
				TargetAttributes = {
					8E61480D2DEAB78F0032F0FE = {
						CreatedOnToolsVersion = 16.2;
					};
					8E6148202DEAB7930032F0FE = {
						CreatedOnToolsVersion = 16.2;
						TestTargetID = 8E61480D2DEAB78F0032F0FE;
					};
					8E61482A2DEAB7930032F0FE = {
						CreatedOnToolsVersion = 16.2;
						TestTargetID = 8E61480D2DEAB78F0032F0FE;
					};
				};
			};
			buildConfigurationList = 8E6148092DEAB78F0032F0FE /* Build configuration list for PBXProject "MacChessBase" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 8E6148052DEAB78F0032F0FE;
			minimizedProjectReferenceProxies = 1;
			packageReferences = (
				8E2811532DF3EDBF008D9593 /* XCLocalSwiftPackageReference "chesskit-swift" */,
				8E2ACA742E39040E00C73355 /* XCLocalSwiftPackageReference "../../SwiftSoup" */,
			);
			preferredProjectObjectVersion = 77;
			productRefGroup = 8E61480F2DEAB78F0032F0FE /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				8E61480D2DEAB78F0032F0FE /* MacChessBase */,
				8E6148202DEAB7930032F0FE /* MacChessBaseTests */,
				8E61482A2DEAB7930032F0FE /* MacChessBaseUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		8E61480C2DEAB78F0032F0FE /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		8E61481F2DEAB7930032F0FE /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		8E6148292DEAB7930032F0FE /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		8E61480A2DEAB78F0032F0FE /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		8E61481D2DEAB7930032F0FE /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		8E6148272DEAB7930032F0FE /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		8E6148232DEAB7930032F0FE /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 8E61480D2DEAB78F0032F0FE /* MacChessBase */;
			targetProxy = 8E6148222DEAB7930032F0FE /* PBXContainerItemProxy */;
		};
		8E61482D2DEAB7930032F0FE /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 8E61480D2DEAB78F0032F0FE /* MacChessBase */;
			targetProxy = 8E61482C2DEAB7930032F0FE /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		8E6148332DEAB7930032F0FE /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MACOSX_DEPLOYMENT_TARGET = 15.2;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = macosx;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		8E6148342DEAB7930032F0FE /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MACOSX_DEPLOYMENT_TARGET = 15.2;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = macosx;
				SWIFT_COMPILATION_MODE = wholemodule;
			};
			name = Release;
		};
		8E6148362DEAB7930032F0FE /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ALLOW_ENTITLEMENTS_MODIFICATION = YES;
				CODE_SIGN_ENTITLEMENTS = MacChessBase/MacChessBase.entitlements;
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"MacChessBase/Preview Content\"";
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				MACOSX_DEPLOYMENT_TARGET = 14.0;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = ChessWorld.MacChessBase;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
			};
			name = Debug;
		};
		8E6148372DEAB7930032F0FE /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ALLOW_ENTITLEMENTS_MODIFICATION = YES;
				CODE_SIGN_ENTITLEMENTS = MacChessBase/MacChessBase.entitlements;
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"MacChessBase/Preview Content\"";
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				MACOSX_DEPLOYMENT_TARGET = 14.0;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = ChessWorld.MacChessBase;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
			};
			name = Release;
		};
		8E6148392DEAB7930032F0FE /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				"FRAMEWORK_SEARCH_PATHS[arch=*]" = "$(BUILT_PRODUCTS_DIR)";
				GENERATE_INFOPLIST_FILE = YES;
				MACOSX_DEPLOYMENT_TARGET = 14.0;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = ChessWorld.MacChessBaseTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/MacChessBase.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/MacChessBase";
			};
			name = Debug;
		};
		8E61483A2DEAB7930032F0FE /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				MACOSX_DEPLOYMENT_TARGET = 14.0;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = ChessWorld.MacChessBaseTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/MacChessBase.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/MacChessBase";
			};
			name = Release;
		};
		8E61483C2DEAB7930032F0FE /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = ChessWorld.MacChessBaseUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TEST_TARGET_NAME = MacChessBase;
			};
			name = Debug;
		};
		8E61483D2DEAB7930032F0FE /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = ChessWorld.MacChessBaseUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TEST_TARGET_NAME = MacChessBase;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		8E6148092DEAB78F0032F0FE /* Build configuration list for PBXProject "MacChessBase" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				8E6148332DEAB7930032F0FE /* Debug */,
				8E6148342DEAB7930032F0FE /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		8E6148352DEAB7930032F0FE /* Build configuration list for PBXNativeTarget "MacChessBase" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				8E6148362DEAB7930032F0FE /* Debug */,
				8E6148372DEAB7930032F0FE /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		8E6148382DEAB7930032F0FE /* Build configuration list for PBXNativeTarget "MacChessBaseTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				8E6148392DEAB7930032F0FE /* Debug */,
				8E61483A2DEAB7930032F0FE /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		8E61483B2DEAB7930032F0FE /* Build configuration list for PBXNativeTarget "MacChessBaseUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				8E61483C2DEAB7930032F0FE /* Debug */,
				8E61483D2DEAB7930032F0FE /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCLocalSwiftPackageReference section */
		8E2811532DF3EDBF008D9593 /* XCLocalSwiftPackageReference "chesskit-swift" */ = {
			isa = XCLocalSwiftPackageReference;
			relativePath = "chesskit-swift";
		};
		8E2ACA742E39040E00C73355 /* XCLocalSwiftPackageReference "../../SwiftSoup" */ = {
			isa = XCLocalSwiftPackageReference;
			relativePath = ../../SwiftSoup;
		};
/* End XCLocalSwiftPackageReference section */

/* Begin XCSwiftPackageProductDependency section */
		8E2811542DF3EDBF008D9593 /* ChessKit */ = {
			isa = XCSwiftPackageProductDependency;
			productName = ChessKit;
		};
		8E2ACA752E39040E00C73355 /* SwiftSoup */ = {
			isa = XCSwiftPackageProductDependency;
			productName = SwiftSoup;
		};
		8E7A115D2E1DF17C00880849 /* ChessKit */ = {
			isa = XCSwiftPackageProductDependency;
			package = 8E2811532DF3EDBF008D9593 /* XCLocalSwiftPackageReference "chesskit-swift" */;
			productName = ChessKit;
		};
/* End XCSwiftPackageProductDependency section */
	};
	rootObject = 8E6148062DEAB78F0032F0FE /* Project object */;
}
