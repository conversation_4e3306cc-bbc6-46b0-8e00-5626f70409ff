{"configurations": [{"id": "4E97B8E0-7C2B-4CA7-8821-76791B163EA4", "name": "Test Scheme Action", "options": {}}], "defaultOptions": {"targetForVariableExpansion": {"containerPath": "container:MacChessBase.xcodeproj", "identifier": "8E61480D2DEAB78F0032F0FE", "name": "MacChessBase"}}, "testTargets": [{"parallelizable": true, "skippedTests": ["ChessGameViewModelTests/testFlipBoard()", "ChessGameViewModelTests/testLoadGameFromFEN()", "ChessGameViewModelTests/testMakeCapture()", "ChessGameViewModelTests/testSquareSelectionWithMove()", "ChessKitExtensionsTests", "ChessKitExtensionsTests/testAllSquareTransformations()", "ChessKitExtensionsTests/testBoardEdgeSquares()", "ChessKitExtensionsTests/testBoardSizeCalculations()", "ChessKitExtensionsTests/testBoardSizeCalculationsWithDifferentSizes()", "ChessKitExtensionsTests/testCoordinateRangeValidation()", "ChessKitExtensionsTests/testCoordinateTransformPerformance()", "ChessKitExtensionsTests/testGameExtensions()", "ChessKitExtensionsTests/testMoveExtensions()", "ChessKitExtensionsTests/testNegativeCoordinates()", "ChessKitExtensionsTests/testPieceExtensions()", "ChessKitExtensionsTests/testPointOutsideBoard()", "ChessKitExtensionsTests/testPointToSquarePerformance()", "ChessKitExtensionsTests/testPointToSquareTransform()", "ChessKitExtensionsTests/testPointToSquareTransformFlipped()", "ChessKitExtensionsTests/testPositionExtensions()", "ChessKitExtensionsTests/testSquareExtensions()", "ChessKitExtensionsTests/testSquareToCoordinateTransform()", "ChessKitExtensionsTests/testSquareToCoordinateTransformFlipped()", "ChessKitExtensionsTests/testTransformationConsistency()", "ChessKitExtensionsTests/testVeryLargeCoordinates()", "FENValidationTests/testCastlingRightsLogicValidation()", "MacChessBaseTests", "MacChessBaseTests/example()"], "target": {"containerPath": "container:MacChessBase.xcodeproj", "identifier": "8E6148202DEAB7930032F0FE", "name": "MacChessBaseTests"}}, {"parallelizable": true, "skippedTests": ["MacChessBaseUITests", "MacChessBaseUITests/testAccessibilityElements()", "MacChessBaseUITests/testBoardRenderingPerformance()", "MacChessBaseUITests/testChessBoardInteraction()", "MacChessBaseUITests/testEditMenu()", "MacChessBaseUITests/testEngineAnalysisView()", "MacChessBaseUITests/testExample()", "MacChessBaseUITests/testFileMenu()", "MacChessBaseUITests/testGameMenu()", "MacChessBaseUITests/testGameSessionSidebar()", "MacChessBaseUITests/testInvalidPGNImport()", "MacChessBaseUITests/testKeyboardShortcuts()", "MacChessBaseUITests/testLaunchPerformance()", "MacChessBaseUITests/testMoveNotationView()", "MacChessBaseUITests/testMultipleWindows()", "MacChessBaseUITests/testNavigationPerformance()", "MacChessBaseUITests/testPositionEditor()", "MacChessBaseUITests/testWindowResize()", "MacChessBaseUITestsLaunchTests", "MacChessBaseUITestsLaunchTests/testLaunch()"], "target": {"containerPath": "container:MacChessBase.xcodeproj", "identifier": "8E61482A2DEAB7930032F0FE", "name": "MacChessBaseUITests"}}], "version": 1}