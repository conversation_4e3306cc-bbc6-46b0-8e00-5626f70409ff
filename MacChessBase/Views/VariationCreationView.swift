//
//  VariationCreationView.swift
//  MacChessBase
//
//  Created on 2025/6/22.
//

import SwiftUI
import ChessKit

/// A view that presents options for creating a new variation
struct VariationCreationView: View {
    @ObservedObject var viewModel: ChessGameViewModel
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        VStack(spacing: 0) {
            // Header
            VStack(spacing: 8) {
                Text("New Move Options")
                    .font(.headline)
                    .foregroundColor(.primary)
                
                if let move = viewModel.variationManager.pendingMove {
                    Text("Move: \(move.metaMove!.san)")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
            }
            .padding(.top, 16)
            .padding(.horizontal, 16)
            
            Divider()
                .padding(.vertical, 12)
            
            // Options
            VStack(spacing: 8) {
                VariationOptionButton(
                    title: "New Variation",
                    description: "Add this move as a new variation",
                    icon: "plus.circle",
                    color: .blue
                ) {
                    viewModel.selectVariationCreationOption(.newVariation)
                }
                
                VariationOptionButton(
                    title: "New Main Line",
                    description: "Add as variation and promote to main line",
                    icon: "star.circle",
                    color: .orange
                ) {
                    viewModel.selectVariationCreationOption(.newMainLine)
                }
                
                VariationOptionButton(
                    title: "Overwrite",
                    description: "Replace the existing move with this one",
                    icon: "arrow.clockwise.circle",
                    color: .red
                ) {
                    viewModel.selectVariationCreationOption(.overwrite)
                }
            }
            .padding(.horizontal, 16)
            
            Divider()
                .padding(.vertical, 12)
            
            // Cancel button
            HStack {
                Spacer()
                Button("Cancel") {
                    viewModel.cancelVariationCreation()
                }
                .buttonStyle(.bordered)
                .padding(.horizontal, 16)
                .padding(.bottom, 16)
            }
        }
        .frame(width: 320)
        .background(Color(NSColor.controlBackgroundColor))
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.3), radius: 10, x: 0, y: 5)
    }
}

/// A button for variation creation options
struct VariationOptionButton: View {
    let title: String
    let description: String
    let icon: String
    let color: Color
    var isRecommended: Bool = false
    let action: () -> Void
    
    @State private var isHovered = false
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 12) {
                Image(systemName: icon)
                    .font(.title2)
                    .foregroundColor(color)
                    .frame(width: 24, height: 24)
                
                VStack(alignment: .leading, spacing: 2) {
                    HStack {
                        Text(title)
                            .font(.subheadline)
                            .fontWeight(.medium)
                            .foregroundColor(.primary)
                        
                        if isRecommended {
                            Text("RECOMMENDED")
                                .font(.caption2)
                                .fontWeight(.bold)
                                .foregroundColor(.white)
                                .padding(.horizontal, 6)
                                .padding(.vertical, 2)
                                .background(Color.blue)
                                .cornerRadius(4)
                        }
                        
                        Spacer()
                    }
                    
                    Text(description)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.leading)
                }
                
                Spacer()
                
                Image(systemName: "chevron.right")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 10)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(isHovered ? Color(NSColor.controlBackgroundColor) : Color.clear)
                    .overlay(
                        RoundedRectangle(cornerRadius: 8)
                            .stroke(isHovered ? color.opacity(0.3) : Color.clear, lineWidth: 1)
                    )
            )
        }
        .buttonStyle(.plain)
        .onHover { hovering in
            withAnimation(.easeInOut(duration: 0.2)) {
                isHovered = hovering
            }
        }
    }
}

#Preview {
    VariationCreationView(viewModel: ChessGameViewModel())
        .padding()
}
