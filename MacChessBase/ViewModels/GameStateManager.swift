//
//  GameStateManager.swift
//  MacChessBase
//
//  Created by Assistant on 2025/8/3.
//

import SwiftUI
import ChessKit
import Combine

/// Manages game state, promotion handling, and result detection
@MainActor
class GameStateManager: ObservableObject {
    // MARK: - Session Reference
    private var sessionProvider: (() -> GameSession?)?
    private var session: GameSession? { sessionProvider?() }
    
    // MARK: - Published Properties
    @Published var gameStatus: GameStatus = .inProgress
    @Published var promotionMove: Move?
    @Published var showPromotionDialog = false
    
    // MARK: - Types
    enum GameStatus: Equatable {
        case inProgress
        case checkmate(Piece.Color)
        case stalemate
        case draw(Board.EndResult.DrawType)
    }
    
    // MARK: - Initialization
    init() {
        // Initialized without a session provider, which will be set later.
    }
    
    // MARK: - Public Methods
    
    /// Sets the session provider for the manager.
    /// This is called by the parent ViewModel after initialization to avoid retain cycles.
    func setSessionProvider(_ sessionProvider: @escaping () -> GameSession?) {
        self.sessionProvider = sessionProvider
    }
    
    /// Resets game state to initial values
    func resetGameState() {
        gameStatus = .inProgress
        promotionMove = nil
        showPromotionDialog = false
    }
    
    /// Checks if a move requires promotion
    func requiresPromotion(_ move: Move) -> Bool {
        guard let metaMove = move.metaMove else { return false }
        
        return metaMove.piece.kind == .pawn &&
               (metaMove.end.rank.value == 8 || metaMove.end.rank.value == 1) &&
               metaMove.promotedPiece == nil
    }
    
    /// Handles promotion setup for a move
    func handlePromotionSetup(for move: Move) {
        if requiresPromotion(move) {
            promotionMove = move
            showPromotionDialog = true
        }
    }
    
    /// Completes a pawn promotion
    func completePromotion(to pieceKind: Piece.Kind, board: inout Board) -> Move? {
        guard let promotionMove = promotionMove else { return nil }
        
        let completedMove = board.completePromotion(of: promotionMove, to: pieceKind)
        
        // Clear promotion state
        self.promotionMove = nil
        showPromotionDialog = false
        
        return completedMove
    }
    
    /// Checks the game status after a move
    func checkGameStatus(_ move: Move, board: Board) {
        guard let metaMove = move.metaMove else {
            gameStatus = .inProgress
            return
        }
        
        switch metaMove.checkState {
        case .checkmate:
            gameStatus = .checkmate(board.position.sideToMove.opposite)
        case .stalemate:
            gameStatus = .stalemate
        default:
            // Check for other draw conditions
            if board.position.clock.halfmoves >= 100 {
                gameStatus = .draw(.fiftyMoves)
            } else if board.position.hasInsufficientMaterial {
                gameStatus = .draw(.insufficientMaterial)
            } else {
                gameStatus = .inProgress
            }
        }
    }
    
    /// Auto-detects game result based on current game status
    func autoDetectGameResult() {
        guard let session = session else { return }
        
        // Only update if result is currently unset
        if session.game.metadata.result.isEmpty || session.game.metadata.result == "*" {
            switch gameStatus {
            case .checkmate(let color):
                session.game.metadata.result = color == .white ? "1-0" : "0-1"
            case .stalemate, .draw:
                session.game.metadata.result = "1/2-1/2"
            case .inProgress:
                session.game.metadata.result = "*"
            }
        }
    }
    
    /// Gets the current player's turn
    func getCurrentPlayer(from board: Board) -> Piece.Color {
        return board.position.sideToMove
    }
    
    /// Checks if the game is in progress
    var isGameInProgress: Bool {
        if case .inProgress = gameStatus {
            return true
        }
        return false
    }
    
    /// Gets a human-readable description of the game status
    var gameStatusDescription: String {
        switch gameStatus {
        case .inProgress:
            return "Game in progress"
        case .checkmate(let color):
            return "\(color == .white ? "White" : "Black") wins by checkmate"
        case .stalemate:
            return "Draw by stalemate"
        case .draw(let drawType):
            switch drawType {
            case .fiftyMoves:
                return "Draw by fifty-move rule"
            case .insufficientMaterial:
                return "Draw by insufficient material"
            case .repetition:
                return "Draw by threefold repetition"
            case .stalemate:
                return "Draw by stalemate"
            case .agreement:
                return "Draw by agreement"
            }
        }
    }
}
