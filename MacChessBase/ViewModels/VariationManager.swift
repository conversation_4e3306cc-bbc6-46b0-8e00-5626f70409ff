//
//  VariationManager.swift
//  MacChessBase
//
//  Created by <PERSON> on 2025/8/3.
//

import SwiftUI
import Chess<PERSON>it

@MainActor
class VariationManager: ObservableObject {
    // MARK: - Session Reference
    private var sessionProvider: (() -> GameSession?)?
    private var session: GameSession? { sessionProvider?() }
    
    // MARK: - Published Properties
    
    // Variation Selection
    @Published var showVariationSelection = false
    @Published var availableVariations: [VariationOption] = []
    
    // Variation Creation
    @Published var showVariationCreationDialog = false
    @Published var pendingMove: Move?
    @Published var pendingMoveFromIndex: MoveTree.MoveIndex?
    @Published var existingNextMoveIndex: MoveTree.MoveIndex?
    
    // Keyboard navigation state
    @Published var isKeyboardNavigationDisabled = false  // Used to temporarily disable global keyboard shortcuts
    
    // MARK: - Types
    
    /// Options for creating a new variation
    enum VariationCreationOption {
        case newVariation      // Create new variation (default behavior)
        case newMainLine       // Create new variation and promote it
        case overwrite         // Create new variation and delete existing move
    }
    
    /// Represents a variation option for user selection
    struct VariationOption: Identifiable {
        let id: Int
        let index: MoveTree.MoveIndex
        let pgnText: String
        let isMainLine: Bool
    }
    
    // MARK: - Initialization
    init() {
        // Initialized without a session provider, which will be set later.
    }
    
    // MARK: - Public Methods
    
    /// Sets the session provider for the manager.
    /// This is called by the parent ViewModel after initialization to avoid retain cycles.
    func setSessionProvider(_ sessionProvider: @escaping () -> GameSession?) {
        self.sessionProvider = sessionProvider
    }
    
    /// Shows the variation selection dialog
    func showVariationSelectionDialog(mainLineIndex: MoveTree.MoveIndex?, variationIndices: [MoveTree.MoveIndex]) {
        var options: [VariationOption] = []
        
        // Add main line option if available
        if let mainLineIndex = mainLineIndex {
            let mainLinePGN = generatePGNTextForMove(at: mainLineIndex)
            options.append(VariationOption(
                id: 0,
                index: mainLineIndex,
                pgnText: mainLinePGN,
                isMainLine: true
            ))
        }
        
        // Add variation options
        for (i, variationIndex) in variationIndices.enumerated() {
            let variationPGN = generatePGNTextForMove(at: variationIndex)
            options.append(VariationOption(
                id: i+1,
                index: variationIndex,
                pgnText: variationPGN,
                isMainLine: false
            ))
        }
        
        // Sort by main line first, then by move hash for consistent ordering
        options.sort(by: { lhs, rhs in
            lhs.id < rhs.id
        })
        
        self.availableVariations = options
        self.isKeyboardNavigationDisabled = true  // Disable global navigation shortcuts
        self.showVariationSelection = true
    }
    
    /// Selects a variation and navigates to it
    func selectVariation(_ option: VariationOption) {
        self.showVariationSelection = false
        self.availableVariations = []
        self.isKeyboardNavigationDisabled = false  // Re-enable global navigation shortcuts
        
        // Navigate to the selected variation through the session
        self.session?.goToMove(at: option.index)
    }
    
    /// Cancels variation selection
    func cancelVariationSelection() {
        self.showVariationSelection = false
        self.availableVariations = []
        self.isKeyboardNavigationDisabled = false  // Re-enable global navigation shortcuts
    }
    
    /// Handles variation creation option selection
    func selectVariationCreationOption(_ option: VariationCreationOption, executeMove: @escaping (Move, VariationCreationOption) -> Void) {
        guard let move = pendingMove else { return }
        
        self.showVariationCreationDialog = false
        self.isKeyboardNavigationDisabled = false  // Re-enable navigation
        
        // Add a small delay to ensure UI is fully reset after sheet dismissal
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            executeMove(move, option)
        }
    }
    
    /// Cancels variation creation
    func cancelVariationCreation() {
        self.showVariationCreationDialog = false
        self.isKeyboardNavigationDisabled = false  // Re-enable navigation
        self.pendingMove = nil
        self.pendingMoveFromIndex = nil
        self.existingNextMoveIndex = nil
    }
    
    /// Prepares for variation creation dialog
    func prepareVariationCreation(move: Move, fromIndex: MoveTree.MoveIndex, existingNextIndex: MoveTree.MoveIndex?) {
        self.pendingMove = move
        self.pendingMoveFromIndex = fromIndex
        self.existingNextMoveIndex = existingNextIndex
        self.showVariationCreationDialog = true
        self.isKeyboardNavigationDisabled = true  // Disable navigation while dialog is open
    }
    
    /// Checks if a variation creation dialog should be shown for the given move
    func shouldShowVariationCreationDialog(for move: Move, currentMoveIndex: MoveTree.MoveIndex) -> Bool {
        guard let session = session else { return false }
        
        // Check if there's already a next move that would create a variation
        if session.game.moves.hasNextMove(containing: move, for: currentMoveIndex) != nil {
            // Move already exists, no dialog needed
            return false
        }
        
        // Special handling for the first move (from starting position)
        if currentMoveIndex == session.game.startingIndex {
            // Check if there's already a first move in the game
            if !session.game.moves.isEmpty {
                // There's already a first move, this will create a variation
                return true
            }
        }
        
        // Check if there are any existing moves from this position (main line or variations)
        let mainLineNextIndex = session.game.moves.nextIndex(currentIndex: currentMoveIndex)
        let variations = session.game.moves.variations(from: currentMoveIndex)
        
        // If there's a main line move OR variations, this new move will create a variation
        return mainLineNextIndex != nil || !variations.isEmpty
    }

    // MARK: - Private Methods

    /// Generates PGN text for a move and its continuation
    private func generatePGNTextForMove(at index: MoveTree.MoveIndex) -> String {
        guard let session = session else { return "" }

        // Use the variationPGN method to get the proper PGN representation
        let pgnElements = session.game.moves.variationPGN(from: index)

        var result = ""
        var moveCount = 0
        let maxMovesToShow = 6 // Show up to 3 move pairs

        for element in pgnElements {
            if moveCount >= maxMovesToShow {
                result += "..."
                break
            }

            switch element {
            case .whiteNumber(let number):
                result += "\(number). "

            case .blackNumber(let number):
                result += "\(number)... "

            case .move(let move, _):
                result += move.metaMove?.san ?? ""
                if !result.isEmpty && !result.hasSuffix(".") && !result.hasSuffix("…") {
                    result += " "
                }
                moveCount += 1

            case .variationStart, .variationEnd:
                // Skip variation markers in the preview
                break
            }
        }

        return result.trimmingCharacters(in: .whitespaces)
    }
}
