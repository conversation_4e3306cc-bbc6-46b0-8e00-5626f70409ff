//
//  EngineManagerTests.swift
//  MacChessBaseTests
//
//  Tests for Stockfish engine integration
//

import XCTest
@testable import MacChessBase
@testable import ChessKit

final class EngineManagerTests: XCTestCase {
    
    var engineManager: EngineManager!
    
    @MainActor
    override func setUp() {
        super.setUp()
        engineManager = EngineManager()
    }
    
    @MainActor
    override func tearDown() async throws {
        if engineManager.state != .stopped {
            await engineManager.stopEngine()
        }
        engineManager = nil
        try await super.tearDown()
    }
    
    // MARK: - Engine Lifecycle Tests
    
    @MainActor
    func testEngineStartup() async {
        XCTAssertEqual(engineManager.state, .stopped)
        
        await engineManager.startEngine()
        
        XCTAssertEqual(engineManager.state, .idle)
    }
    
    @MainActor
    func testEngineShutdown() async {
        // Start engine first
        await engineManager.startEngine()
        XCTAssertEqual(engineManager.state, .idle)
        
        // Stop engine
        await engineManager.stopEngine()
        
        XCTAssertEqual(engineManager.state, .stopped)
    }
    
    @MainActor
    func testEngineRestart() async {
        // Start engine
        await engineManager.startEngine()
        XCTAssertEqual(engineManager.state, .idle)
        
        // Stop and restart
        await engineManager.stopEngine()
        XCTAssertEqual(engineManager.state, .stopped)
        
        await engineManager.startEngine()
        
        XCTAssertEqual(engineManager.state, .idle)
    }
    
    // MARK: - Position Analysis Tests
    
    @MainActor
    func testAnalyzeStandardPosition() async {
        await engineManager.startEngine()
        XCTAssertEqual(engineManager.state, .idle)
        
        await engineManager.analyzePosition(Position.standard)
        
        // A short sleep to allow analysis to start
        try? await Task.sleep(nanoseconds: 400_000_000)
        
        XCTAssertEqual(engineManager.state, .analyzing)
    }
    
    @MainActor
    func testAnalyzeCustomPosition() async {
        let customFEN = "rnbqkbnr/pppppppp/8/8/4P3/8/PPPP1PPP/RNBQKBNR b KQkq e3 0 1"
        guard let customPosition = Position(fen: customFEN) else {
            XCTFail("Failed to create position from FEN: \(customFEN)")
            return
        }
        
        await engineManager.startEngine()
        
        await engineManager.analyzePosition(customPosition)
        
        // A short sleep to allow analysis to start
        try? await Task.sleep(nanoseconds: 400_000_000)
        
        XCTAssertEqual(engineManager.state, .analyzing)
    }
    
    @MainActor
    func testAnalyzeComplexPosition() async {
        // Middle game position with tactical opportunities
        let complexFEN = "r1bqkb1r/pppp1ppp/2n2n2/4p3/2B1P3/5N2/PPPP1PPP/RNBQK2R w KQkq - 4 4"
        guard let complexPosition = Position(fen: complexFEN) else {
            XCTFail("Failed to create position from FEN: \(complexFEN)")
            return
        }
        
        await engineManager.startEngine()
        
        await engineManager.analyzePosition(complexPosition)
        
        // A short sleep to allow analysis to start
        try? await Task.sleep(nanoseconds: 400_000_000)
        
        XCTAssertEqual(engineManager.state, .analyzing)
    }
    
    // MARK: - Engine Settings Tests
    
    @MainActor
    func testEngineDepthSetting() async {
        await engineManager.startEngine()
        
        let originalDepth = engineManager.maxDepth
        let newDepth = 15
        
        engineManager.maxDepth = newDepth
        XCTAssertEqual(engineManager.maxDepth, newDepth)
        XCTAssertNotEqual(engineManager.maxDepth, originalDepth)
    }
    
    @MainActor
    func testEngineThreadsSetting() async {
        await engineManager.startEngine()
        
        let _ = engineManager.threadsCount
        let newThreads = 4
        
        engineManager.threadsCount = newThreads
        XCTAssertEqual(engineManager.threadsCount, newThreads)
    }
    
    @MainActor
    func testEngineHashSizeSetting() async {
        await engineManager.startEngine()
        
        let _ = engineManager.hashSizeMB
        let newHashSize = 256
        
        engineManager.hashSizeMB = newHashSize
        XCTAssertEqual(engineManager.hashSizeMB, newHashSize)
    }
    
    @MainActor
    func testEngineMultiPVSetting() async {
        await engineManager.startEngine()
        
        let _ = engineManager.maxLines
        let newMultiPV = 3
        
        engineManager.maxLines = newMultiPV
        XCTAssertEqual(engineManager.maxLines, newMultiPV)
    }
    
    // MARK: - Analysis Control Tests
    
    @MainActor
    func testPauseAndResumeAnalysis() async {
        await engineManager.startEngine()
        
        await engineManager.analyzePosition(Position.standard)
        // A short sleep to allow analysis to start
        try? await Task.sleep(nanoseconds: 400_000_000)
        XCTAssertEqual(engineManager.state, .analyzing)
        
        // Pause analysis
        await engineManager.pauseAnalysis()
        XCTAssertEqual(engineManager.state, .paused)
        
        // Resume analysis
        await engineManager.resumeAnalysis(with: Position.standard)
        // A short sleep to allow analysis to start
        try? await Task.sleep(nanoseconds: 400_000_000)
        XCTAssertEqual(engineManager.state, .analyzing)
    }
    
    @MainActor
    func testClearAnalysis() async {
        await engineManager.startEngine()
        
        await engineManager.analyzePosition(Position.standard)
        // A short sleep to allow analysis to start
        try? await Task.sleep(nanoseconds: 400_000_000)
        
        // Clear analysis
        await engineManager.clearAnalysisResults()
        
        // Verify analysis is cleared
        XCTAssertTrue(engineManager.engineLines.isEmpty)
        XCTAssertNil(engineManager.currentEvaluation)
        XCTAssertEqual(engineManager.state, .stopped)
    }
    
    // MARK: - Multi-PV Analysis Tests
    
    @MainActor
    func testMultiPVAnalysis() async {
        engineManager.maxLines = 3
        
        await engineManager.startEngine()
        
        await engineManager.analyzePosition(Position.standard)
        
        // Give engine time to analyze multiple lines
        try? await Task.sleep(nanoseconds: 1_000_000_000) // 1 second
        
        // Check if we have multiple principal variations
        XCTAssertTrue(engineManager.engineLines.count >= 1)
    }
    
    // MARK: - Error Handling Tests
    
    @MainActor
    func testEngineStartupFailure() async {
        // Test with invalid engine path
        let invalidEngineManager = EngineManager(enginePath: "/invalid/path/to/engine")
        
        await invalidEngineManager.startEngine()
        
        XCTAssertEqual(invalidEngineManager.state, .stopped)
    }
    
    @MainActor
    func testInvalidPositionAnalysis() async {
        // This test verifies that the engine handles analysis gracefully
        await engineManager.startEngine()
        
        // Try to analyze a valid position (the engine should handle this gracefully)
        await engineManager.analyzePosition(Position.standard)
        
        // Verify engine is still running after analysis
        XCTAssertNotEqual(engineManager.state, .stopped)
    }
    
    // MARK: - Performance Tests
    
    @MainActor
    func testAnalysisPerformance() async {
        await engineManager.startEngine()
        
        // Measure the time for analysis setup and execution
        let startTime = Date()
        
        await engineManager.analyzePosition(Position.standard)
        try? await Task.sleep(nanoseconds: 2_000_000_000) // 2 seconds for analysis
        await engineManager.stopAnalysis()
        
        let duration = Date().timeIntervalSince(startTime)
        
        // Verify the analysis completed in reasonable time
        XCTAssertLessThan(duration, 10.0, "Analysis should complete within 10 seconds")
    }
    
    @MainActor
    func testMultiplePositionAnalysisPerformance() async {
        let fenStrings = [
            "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1",
            "rnbqkbnr/pppppppp/8/8/4P3/8/PPPP1PPP/RNBQKBNR b KQkq e3 0 1",
            "r1bqkb1r/pppp1ppp/2n2n2/4p3/2B1P3/5N2/PPPP1PPP/RNBQK2R w KQkq - 4 4"
        ]
        
        var positions: [Position] = []
        for fenString in fenStrings {
            guard let position = Position(fen: fenString) else {
                XCTFail("Failed to create position from FEN: \(fenString)")
                return
            }
            positions.append(position)
        }
        
        await engineManager.startEngine()
        
        let startTime = Date()
        
        for position in positions {
            await engineManager.analyzePosition(position)
            try? await Task.sleep(nanoseconds: 1_000_000_000) // 1 second
            await engineManager.stopAnalysis()
        }
        
        let duration = Date().timeIntervalSince(startTime)
        
        // Verify all positions were analyzed in reasonable time
        XCTAssertLessThan(duration, 15.0, "Multiple position analysis should complete within 15 seconds")
    }
    
    // MARK: - Engine Communication Tests
    
    @MainActor
    func testEngineVersion() async {
        await engineManager.startEngine()
        
        // Check if engine info was received
        XCTAssertNotNil(engineManager.engineInfo)
    }
    
    @MainActor
    func testEngineOptions() async {
        await engineManager.startEngine()
        
        // Engine should be running after successful initialization
        XCTAssertNotEqual(engineManager.state, .stopped)
    }
    
    // MARK: - Memory Management Tests
    
    @MainActor
    func testEngineMemoryUsage() async {
        await engineManager.startEngine()
        
        // Set a specific hash size
        engineManager.hashSizeMB = 128
        await engineManager.applyEngineSettings()
        
        // Analyze multiple positions to fill hash
        let fenStrings = [
            "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1",
            "rnbqkbnr/pppppppp/8/8/4P3/8/PPPP1PPP/RNBQKBNR b KQkq e3 0 1",
            "r1bqkb1r/pppp1ppp/2n2n2/4p3/2B1P3/5N2/PPPP1PPP/RNBQK2R w KQkq - 4 4"
        ]
        
        for fenString in fenStrings {
            guard let position = Position(fen: fenString) else {
                XCTFail("Failed to create position from FEN: \(fenString)")
                return
            }
            
            await engineManager.analyzePosition(position)
            try? await Task.sleep(nanoseconds: 1_000_000_000) // 1 second
            await engineManager.stopAnalysis()
        }
        
        // Engine should still be responsive
        XCTAssertNotEqual(engineManager.state, .stopped)
    }
}
